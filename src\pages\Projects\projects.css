.project-card {
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

/* .project-card:hover {
    transform: scale(1.05) rotate(1deg);
} */

.card-inner {
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.project-card:hover .card-inner {
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Text truncation for descriptions */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth image hover effects */
.project-card img {
    transition: transform 0.3s ease;
}

.project-card:hover img {
    transform: scale(1.1);
}

/* Technology badge hover effects */
.tech-badge {
    transition: all 0.2s ease;
}

.tech-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Button hover animations */
.project-button {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.project-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.project-button:hover::before {
    left: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-card {
        margin-bottom: 1rem;
    }
    
    .project-card:hover {
        transform: scale(1.02) rotate(0.5deg);
    }
}

/* Dark mode specific styles */
.dark .card-inner {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .project-card:hover .card-inner {
    background: rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.2);
}

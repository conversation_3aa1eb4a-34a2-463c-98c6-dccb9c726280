.card {

    transition: all 0.2s;
    position: relative;
    cursor: pointer;
}

.card-inner {
    width: inherit;
    height: inherit;
    background: rgba(255, 255, 255, .05);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(5px);
    border-radius: 8px;
    transform: scale(1) ;
}

.circle {

    border-radius: 50%;
    position: absolute;
    animation: move-up6 2s ease-in infinite alternate-reverse;
}

.circle:nth-child(1) {
    top: -15px;
    left: -15px;
}

.circle:nth-child(2) {
    bottom: -15px;
    right: -15px;
    animation-name: move-down1;
}

@keyframes move-up6 {
    to {
        transform: translateY(-10px);
    }
}

@keyframes move-down1 {
    to {
        transform: translateY(10px);
    }
}


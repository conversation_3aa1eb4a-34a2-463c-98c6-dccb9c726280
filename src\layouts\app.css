@font-face {
    font-family: 'Briem-Extrabold';
    src: url('../assets/fonts/static/BriemHand-ExtraBold.ttf') format('woff2'),
        /* url('../fonts/font.woff') format('woff'), */
        /* url('../fonts/font.ttf') format('truetype'); */
        /* Add additional font formats if necessary */
        /* Specify the correct path to the font files */
}

@font-face {
    font-family: 'Briem-bold';
    src: url('../assets/fonts/static/BriemHand-Bold.ttf') format('woff2'),
        /* url('../fonts/font.woff') format('woff'), */
        /* url('../fonts/font.ttf') format('truetype'); */
        /* Add additional font formats if necessary */
        /* Specify the correct path to the font files */
}

@font-face {
    font-family: 'Briem-medium';
    src: url('../assets/fonts/static/BriemHand-Medium.ttf') format('woff2'),
        /* url('../fonts/font.woff') format('woff'), */
        /* url('../fonts/font.ttf') format('truetype'); */
        /* Add additional font formats if necessary */
        /* Specify the correct path to the font files */
}

@font-face {
    font-family: 'Briem-light';
    src: url('../assets/fonts/static/BriemHand-Light.ttf') format('woff2'),
        /* url('../fonts/font.woff') format('woff'), */
        /* url('../fonts/font.ttf') format('truetype'); */
        /* Add additional font formats if necessary */
        /* Specify the correct path to the font files */
}

.hamburger {
    cursor: pointer;
}

.hamburger input {
    display: none;
}

.hamburger svg {
    /* The size of the SVG defines the overall size */
    height: 3em;
    /* Define the transition for transforming the SVG */
    -webkit-transition: -webkit-transform 600ms cubic-bezier(0.4, 0, 0.2, 1);
    transition: -webkit-transform 600ms cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 600ms cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 600ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.line {
    fill: none;
    /* stroke: white; */
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 3;
    /* Define the transition for transforming the Stroke */
    -webkit-transition: stroke-dasharray 600ms cubic-bezier(0.4, 0, 0.2, 1),
        stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1);
    transition: stroke-dasharray 600ms cubic-bezier(0.4, 0, 0.2, 1),
        stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.line-top-bottom {
    stroke-dasharray: 12 63;
}

.hamburger input:checked+svg {
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.hamburger input:checked+svg .line-top-bottom {
    stroke-dasharray: 20 300;
    stroke-dashoffset: -32.42;
}





.switch-name {
    --dark-sky: transparent;
    --moon: white;
    --light-sky: transparent;
    --sun: black;
    display: inline-block;
    position: relative;
    border-radius: 5em;
    cursor: pointer;
    width: 70px;
    height: 35px;
    overflow: hidden;
    transition: all .5s;
}

.switch-name .back {
    background-color: var(--dark-sky);
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transition: .5s ease-in-out;
}

.switch-name .checkbox {
    opacity: 0;
}

.switch-name .checkbox:checked~.back {
    background-color: var(--light-sky);
}

.switch-name .checkbox:checked~.moon {
    transform: translate(100%) rotate(180deg);
    opacity: 0;
}

.switch-name .checkbox:checked~.sun {
    transform: translate(100%) rotate(180deg);
    opacity: 1;
}

.switch-name .moon {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: .4em;
    fill: var(--moon);
    transition: .5s;
}

.switch-name .sun {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: .4em;
    fill: var(--sun);
    transition: .5s;
    opacity: 0;
}
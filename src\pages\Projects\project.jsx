import React, { useContext, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
    FaReact, FaNodeJs, FaPython, FaHtml5, FaCss3, FaJs, FaGithub, FaExternalLinkAlt, FaDatabase, FaMobile, FaArrowLeft, FaCheck, FaCog, FaLightbulb
} from 'react-icons/fa';
import {
    SiTailwindcss, SiMongodb, SiExpress, SiFirebase, SiTypescript, SiNextdotjs, SiFlutter, SiDjango
} from 'react-icons/si';
import ParticlesComponentlight from '../Layout/components/particleslight';
import ParticlesComponentDark from '../Layout/components/particlesdark';
import { MyContext } from '../../utils/ContextProvider';
import { ProjectHeader } from '../../layouts/ProjectHeader';
import './projects.css';

export const Project = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [dark] = useContext(MyContext);

    useEffect(() => {
        if (dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }, [dark]);

    const projects = [
        {
            id: 1,
            title: "E-Commerce Platform",
            description: "A full-stack e-commerce platform with user authentication, product management, shopping cart, and payment integration. Features include admin dashboard and order tracking.",
            detailedDescription: "This comprehensive e-commerce platform was built to provide a complete online shopping experience. The application features a modern, responsive design with intuitive user interfaces for both customers and administrators. Key functionalities include user registration and authentication, product catalog management, shopping cart operations, secure payment processing, and order tracking systems.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "Node.js", icon: <FaNodeJs />, color: "#339933" },
                { name: "MongoDB", icon: <SiMongodb />, color: "#47A248" },
                { name: "Express", icon: <SiExpress />, color: "#000000" }
            ],
            image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=600&fit=crop",
            liveDemo: "https://demo-ecommerce.com",
            github: "https://github.com/yahya/ecommerce-platform",
            features: [
                "User authentication and authorization",
                "Product catalog with search and filtering",
                "Shopping cart and wishlist functionality",
                "Secure payment integration with Stripe",
                "Admin dashboard for inventory management",
                "Order tracking and history",
                "Responsive design for all devices",
                "Email notifications for orders"
            ],
            challenges: [
                "Implementing secure payment processing",
                "Optimizing database queries for large product catalogs",
                "Creating a scalable admin dashboard",
                "Ensuring responsive design across all devices"
            ],
            developmentProcess: "The project followed an agile development methodology with iterative sprints. Started with wireframing and user experience design, then moved to backend API development, followed by frontend implementation and finally integration testing."
        },
        {
            id: 2,
            title: "Task Management App",
            description: "A collaborative task management application with real-time updates, team collaboration features, and project tracking capabilities.",
            detailedDescription: "A modern task management application designed for teams and individuals to organize, track, and collaborate on projects efficiently. Built with Next.js and TypeScript for optimal performance and type safety, featuring real-time updates and seamless user experience.",
            technologies: [
                { name: "Next.js", icon: <SiNextdotjs />, color: "#000000" },
                { name: "TypeScript", icon: <SiTypescript />, color: "#3178C6" },
                { name: "Tailwind", icon: <SiTailwindcss />, color: "#06B6D4" },
                { name: "Firebase", icon: <SiFirebase />, color: "#FFCA28" }
            ],
            image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=600&fit=crop",
            liveDemo: "https://taskmanager-demo.com",
            github: "https://github.com/yahya/task-manager",
            features: [
                "Real-time task updates and notifications",
                "Team collaboration and member management",
                "Project organization with boards and lists",
                "Due date tracking and reminders",
                "File attachments and comments",
                "Progress tracking and analytics",
                "Mobile-responsive design",
                "Dark/light theme support"
            ],
            challenges: [
                "Implementing real-time synchronization across multiple users",
                "Optimizing performance for large datasets",
                "Creating intuitive drag-and-drop interfaces",
                "Managing complex state with TypeScript"
            ],
            developmentProcess: "Developed using modern React patterns with Next.js for server-side rendering and optimal performance. Utilized Firebase for real-time database operations and authentication, ensuring seamless collaboration features."
        },
        {
            id: 3,
            title: "Weather Dashboard",
            description: "A responsive weather dashboard that provides current weather conditions, forecasts, and weather maps with location-based services.",
            detailedDescription: "An intuitive weather dashboard application that provides comprehensive weather information including current conditions, extended forecasts, and interactive weather maps. Features location-based services and beautiful data visualizations.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "JavaScript", icon: <FaJs />, color: "#F7DF1E" },
                { name: "CSS3", icon: <FaCss3 />, color: "#1572B6" },
                { name: "API", icon: <FaDatabase />, color: "#FF6B6B" }
            ],
            image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=1200&h=600&fit=crop",
            liveDemo: "https://weather-dashboard-demo.com",
            github: "https://github.com/yahya/weather-dashboard",
            features: [
                "Current weather conditions display",
                "5-day weather forecast",
                "Interactive weather maps",
                "Location-based weather detection",
                "Search functionality for cities worldwide",
                "Weather alerts and notifications",
                "Historical weather data",
                "Responsive design for all devices"
            ],
            challenges: [
                "Integrating multiple weather APIs",
                "Handling geolocation permissions",
                "Creating responsive data visualizations",
                "Managing API rate limits efficiently"
            ],
            developmentProcess: "Built with React hooks for state management and integrated multiple weather APIs for comprehensive data. Focused on creating an intuitive user interface with smooth animations and responsive design principles."
        },
        // Additional projects with basic info for demo
        { id: 4, title: "Social Media Dashboard", detailedDescription: "A comprehensive social media management dashboard.", technologies: [{ name: "Python", icon: <FaPython />, color: "#3776AB" }], image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=600&fit=crop", liveDemo: "https://social-dashboard-demo.com", github: "https://github.com/yahya/social-dashboard", features: ["Analytics dashboard", "Post scheduling", "Multi-platform integration"], challenges: ["API rate limiting", "Data synchronization"], developmentProcess: "Built with Python Django backend and React frontend." },
        { id: 5, title: "Mobile Fitness App", detailedDescription: "A cross-platform mobile fitness application.", technologies: [{ name: "Flutter", icon: <SiFlutter />, color: "#02569B" }], image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=600&fit=crop", liveDemo: "https://fitness-app-demo.com", github: "https://github.com/yahya/fitness-app", features: ["Workout tracking", "Nutrition planning", "Progress monitoring"], challenges: ["Cross-platform compatibility", "Offline functionality"], developmentProcess: "Developed using Flutter for cross-platform compatibility." },
        { id: 6, title: "Portfolio Website", detailedDescription: "A modern, responsive portfolio website.", technologies: [{ name: "React", icon: <FaReact />, color: "#61DAFB" }], image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=1200&h=600&fit=crop", liveDemo: null, github: "https://github.com/yahya/portfolio", features: ["Responsive design", "Smooth animations", "Interactive elements"], challenges: ["Performance optimization", "Cross-browser compatibility"], developmentProcess: "Built with React and modern web technologies." }
    ];

    // Find the current project based on ID
    const currentProject = projects.find(project => project.id === parseInt(id)) || projects[0];

    return (
        <>
            {/* Particle Background */}
            {dark ? <ParticlesComponentlight id="particles" /> : <ParticlesComponentDark id="particles" className="transition duration-1000 ease-in-out" />}

            {/* Header */}
            <ProjectHeader />

            {/* Main Content */}
            <main className={`min-h-screen ${dark ? 'text-black' : 'text-white'} font-[briem-medium] pt-8 pb-20`}>
                <div className="max-w-6xl mx-auto px-4 md:px-8">

                    {/* Back Button */}
                    <button
                        onClick={() => navigate('/')}
                        className={`back-button flex items-center gap-2 mb-8 px-4 py-2 rounded-lg border-2 border-[#2596be] hover:bg-[#2596be] ${dark ? 'text-black hover:text-white' : 'text-white'} transition-colors duration-200 font-[briem-medium]`}
                    >
                        <FaArrowLeft className="text-sm" />
                        Back to Portfolio
                    </button>

                    {/* Hero Section */}
                    <div className="card-inner rounded-xl p-8 mb-8">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">

                            {/* Project Image */}
                            <div className="relative overflow-hidden rounded-lg">
                                <img
                                    src={currentProject.image}
                                    alt={currentProject.title}
                                    className="w-full h-64 md:h-80 object-cover transition-transform duration-300 hover:scale-105"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                            </div>

                            {/* Project Info */}
                            <div className="space-y-6">
                                <h1 className={`text-3xl md:text-4xl lg:text-5xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} hover:text-[#2596be] transition-colors duration-300`}>
                                    {currentProject.title}
                                </h1>

                                <p className={`text-lg md:text-xl font-[briem-medium] ${dark ? 'text-gray-700' : 'text-gray-300'} leading-relaxed`}>
                                    {currentProject.detailedDescription}
                                </p>

                                {/* Action Buttons */}
                                <div className="flex flex-col sm:flex-row gap-4">
                                    {currentProject.liveDemo && (
                                        <a
                                            href={currentProject.liveDemo}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="project-button flex items-center justify-center gap-2 px-6 py-3 bg-[#2596be] hover:bg-[#1e7a9a] text-white rounded-lg font-[briem-medium] text-lg transition-colors duration-200"
                                        >
                                            <FaExternalLinkAlt className="text-sm" />
                                            View Live Demo
                                        </a>
                                    )}
                                    <a
                                        href={currentProject.github}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className={`project-button flex items-center justify-center gap-2 px-6 py-3 border-2 border-[#2596be] hover:bg-[#2596be] ${dark ? 'text-black hover:text-white' : 'text-white'} rounded-lg font-[briem-medium] text-lg transition-colors duration-200`}
                                    >
                                        <FaGithub className="text-lg" />
                                        View Source Code
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Technologies Section */}
                    <div className="card-inner rounded-xl p-8 mb-8">
                        <h2 className={`text-2xl md:text-3xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} mb-6 flex items-center gap-3`}>
                            <FaCog className="text-[#2596be]" />
                            Technologies Used
                        </h2>
                        <div className="flex flex-wrap gap-3">
                            {currentProject.technologies.map((tech, index) => (
                                <span
                                    key={index}
                                    className="tech-badge inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-[briem-medium] bg-white/10 backdrop-blur-sm border border-white/20"
                                    style={{ color: tech.color }}
                                >
                                    <span className="text-lg">{tech.icon}</span>
                                    {tech.name}
                                </span>
                            ))}
                        </div>
                    </div>

                    {/* Features Section */}
                    <div className="card-inner rounded-xl p-8 mb-8">
                        <h2 className={`text-2xl md:text-3xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} mb-6 flex items-center gap-3`}>
                            <FaCheck className="text-[#2596be]" />
                            Key Features
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {currentProject.features?.map((feature, index) => (
                                <div key={index} className="feature-list-item flex items-start gap-3 cursor-pointer">
                                    <FaCheck className="text-[#2596be] mt-1 flex-shrink-0" />
                                    <span className={`font-[briem-medium] ${dark ? 'text-gray-700' : 'text-gray-300'}`}>
                                        {feature}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Challenges & Development Process */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                        {/* Challenges */}
                        <div className="card-inner rounded-xl p-8">
                            <h2 className={`text-2xl md:text-3xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} mb-6 flex items-center gap-3`}>
                                <FaLightbulb className="text-[#2596be]" />
                                Challenges
                            </h2>
                            <div className="space-y-3">
                                {currentProject.challenges?.map((challenge, index) => (
                                    <div key={index} className="challenge-item flex items-start gap-3 cursor-pointer">
                                        <div className="w-2 h-2 bg-[#2596be] rounded-full mt-2 flex-shrink-0"></div>
                                        <span className={`font-[briem-medium] ${dark ? 'text-gray-700' : 'text-gray-300'}`}>
                                            {challenge}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Development Process */}
                        <div className="card-inner rounded-xl p-8">
                            <h2 className={`text-2xl md:text-3xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} mb-6 flex items-center gap-3`}>
                                <FaCog className="text-[#2596be]" />
                                Development Process
                            </h2>
                            <p className={`font-[briem-medium] ${dark ? 'text-gray-700' : 'text-gray-300'} leading-relaxed`}>
                                {currentProject.developmentProcess}
                            </p>
                        </div>
                    </div>

                </div>
            </main>
        </>
    );
};


import React, { useContext } from 'react';
import { Layout } from '../Layout/layout';
import { useParams } from 'react-router-dom';
import {
    FaReact, FaNodeJs, FaPython, FaHtml5, FaCss3, FaJs, FaGithub, FaExternalLinkAlt, FaDatabase, FaMobile
} from 'react-icons/fa';
import {
    SiTailwindcss, SiMongodb, SiExpress, SiFirebase, SiTypescript, SiNextdotjs, SiFlutter, SiDjango
} from 'react-icons/si';
import ParticlesComponentlight from '../Layout/components/particleslight';
import ParticlesComponentDark from '../Layout/components/particlesdark';
import { MyContext } from '../../utils/ContextProvider';

export const Project = () => {

    const { id } = useParams();
    const [dark, toggleBoolean] = useContext(MyContext)


    const projects = [
        {
            id: 1,
            title: "E-Commerce Platform",
            description: "A full-stack e-commerce platform with user authentication, product management, shopping cart, and payment integration. Features include admin dashboard and order tracking.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "Node.js", icon: <FaNodeJs />, color: "#339933" },
                { name: "MongoDB", icon: <SiMongodb />, color: "#47A248" },
                { name: "Express", icon: <SiExpress />, color: "#000000" }
            ],
            image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=500&h=300&fit=crop",
            liveDemo: "https://demo-ecommerce.com",
            github: "https://github.com/yahya/ecommerce-platform",
        },
        {
            id: 2,
            title: "Task Management App",
            description: "A collaborative task management application with real-time updates, team collaboration features, and project tracking capabilities.",
            technologies: [
                { name: "Next.js", icon: <SiNextdotjs />, color: "#000000" },
                { name: "TypeScript", icon: <SiTypescript />, color: "#3178C6" },
                { name: "Tailwind", icon: <SiTailwindcss />, color: "#06B6D4" },
                { name: "Firebase", icon: <SiFirebase />, color: "#FFCA28" }
            ],
            image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
            liveDemo: "https://taskmanager-demo.com",
            github: "https://github.com/yahya/task-manager",
        },
        {
            id: 3,
            title: "Weather Dashboard",
            description: "A responsive weather dashboard that provides current weather conditions, forecasts, and weather maps with location-based services.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "JavaScript", icon: <FaJs />, color: "#F7DF1E" },
                { name: "CSS3", icon: <FaCss3 />, color: "#1572B6" },
                { name: "API", icon: <FaDatabase />, color: "#FF6B6B" }
            ],
            image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=500&h=300&fit=crop",
            liveDemo: "https://weather-dashboard-demo.com",
            github: "https://github.com/yahya/weather-dashboard",
        },
        {
            id: 4,
            title: "Social Media Dashboard",
            description: "A comprehensive social media management dashboard with analytics, post scheduling, and multi-platform integration for content creators.",
            technologies: [
                { name: "Python", icon: <FaPython />, color: "#3776AB" },
                { name: "Django", icon: <SiDjango />, color: "#092E20" },
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "MongoDB", icon: <SiMongodb />, color: "#47A248" }
            ],
            image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
            liveDemo: "https://social-dashboard-demo.com",
            github: "https://github.com/yahya/social-dashboard",
        },
        {
            id: 5,
            title: "Mobile Fitness App",
            description: "A cross-platform mobile fitness application with workout tracking, nutrition planning, and progress monitoring features.",
            technologies: [
                { name: "Flutter", icon: <SiFlutter />, color: "#02569B" },
                { name: "Firebase", icon: <SiFirebase />, color: "#FFCA28" },
                { name: "Mobile", icon: <FaMobile />, color: "#FF6B6B" }
            ],
            image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=300&fit=crop",
            liveDemo: "https://fitness-app-demo.com",
            github: "https://github.com/yahya/fitness-app",
        },
        {
            id: 6,
            title: "Portfolio Website",
            description: "A modern, responsive portfolio website showcasing projects and skills with smooth animations and interactive elements.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "Tailwind", icon: <SiTailwindcss />, color: "#06B6D4" },
                { name: "HTML5", icon: <FaHtml5 />, color: "#E34F26" },
                { name: "CSS3", icon: <FaCss3 />, color: "#1572B6" }
            ],
            image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=500&h=300&fit=crop",
            liveDemo: null,
            github: "https://github.com/yahya/portfolio",
        }
    ];

    return (
        <>
            {dark ? <ParticlesComponentlight id="particles" /> : <ParticlesComponentDark id="particles" className="transition duration-1000 ease-in-out" />}

        </>
    );
};


import React, { useContext, useEffect } from 'react';
import { MyContext } from '../../utils/ContextProvider';
import { Element } from 'react-scroll';
import './projects.css';
import {
    FaReact,
    FaNodeJs,
    FaPython,
    FaHtml5,
    FaCss3,
    FaJs,
    FaGithub,
    FaExternalLinkAlt,
    FaDatabase,
    FaMobile
} from 'react-icons/fa';
import {
    SiTailwindcss,
    SiMongodb,
    SiExpress,
    SiFirebase,
    SiTypescript,
    SiNextdotjs,
    SiFlutter,
    SiDjango
} from 'react-icons/si';
import { useNavigate } from 'react-router-dom';

export const Projects = () => {
    const [dark] = useContext(MyContext);

    useEffect(() => {
        if (dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }, [dark]);

    const navigate = useNavigate();

    // Sample project data
    const projects = [
        {
            id: 1,
            title: "E-Commerce Platform",
            description: "A full-stack e-commerce platform with user authentication, product management, shopping cart, and payment integration. Features include admin dashboard and order tracking.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "Node.js", icon: <FaNodeJs />, color: "#339933" },
                { name: "MongoDB", icon: <SiMongodb />, color: "#47A248" },
                { name: "Express", icon: <SiExpress />, color: "#000000" }
            ],
            image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop",
            liveDemo: "https://demo-ecommerce.com",
            github: "https://github.com/yahya/ecommerce-platform",
        },
        {
            id: 2,
            title: "Task Management App",
            description: "A collaborative task management application with real-time updates, team collaboration features, and project tracking capabilities.",
            technologies: [
                { name: "Next.js", icon: <SiNextdotjs />, color: "#000000" },
                { name: "TypeScript", icon: <SiTypescript />, color: "#3178C6" },
                { name: "Tailwind", icon: <SiTailwindcss />, color: "#06B6D4" },
                { name: "Firebase", icon: <SiFirebase />, color: "#FFCA28" }
            ],
            image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
            liveDemo: "https://taskmanager-demo.com",
            github: "https://github.com/yahya/task-manager",
        },
        {
            id: 3,
            title: "Weather Dashboard",
            description: "A responsive weather dashboard that provides current weather conditions, forecasts, and weather maps with location-based services.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "JavaScript", icon: <FaJs />, color: "#F7DF1E" },
                { name: "CSS3", icon: <FaCss3 />, color: "#1572B6" },
                { name: "API", icon: <FaDatabase />, color: "#FF6B6B" }
            ],
            image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=500&h=300&fit=crop",
            liveDemo: "https://weather-dashboard-demo.com",
            github: "https://github.com/yahya/weather-dashboard",
        },
        {
            id: 4,
            title: "Social Media Dashboard",
            description: "A comprehensive social media management dashboard with analytics, post scheduling, and multi-platform integration for content creators.",
            technologies: [
                { name: "Python", icon: <FaPython />, color: "#3776AB" },
                { name: "Django", icon: <SiDjango />, color: "#092E20" },
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "MongoDB", icon: <SiMongodb />, color: "#47A248" }
            ],
            image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
            liveDemo: "https://social-dashboard-demo.com",
            github: "https://github.com/yahya/social-dashboard",
        },
        {
            id: 5,
            title: "Mobile Fitness App",
            description: "A cross-platform mobile fitness application with workout tracking, nutrition planning, and progress monitoring features.",
            technologies: [
                { name: "Flutter", icon: <SiFlutter />, color: "#02569B" },
                { name: "Firebase", icon: <SiFirebase />, color: "#FFCA28" },
                { name: "Mobile", icon: <FaMobile />, color: "#FF6B6B" }
            ],
            image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=300&fit=crop",
            liveDemo: "https://fitness-app-demo.com",
            github: "https://github.com/yahya/fitness-app",
        },
        {
            id: 6,
            title: "Portfolio Website",
            description: "A modern, responsive portfolio website showcasing projects and skills with smooth animations and interactive elements.",
            technologies: [
                { name: "React", icon: <FaReact />, color: "#61DAFB" },
                { name: "Tailwind", icon: <SiTailwindcss />, color: "#06B6D4" },
                { name: "HTML5", icon: <FaHtml5 />, color: "#E34F26" },
                { name: "CSS3", icon: <FaCss3 />, color: "#1572B6" }
            ],
            image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=500&h=300&fit=crop",
            liveDemo: null,
            github: "https://github.com/yahya/portfolio",
        }
    ];

    return (
        <>
            <Element name='projects' className="text-white dark:text-black font-[briem-medium] md:py-14 lg:py-16 py-20">
                <span className='w-[100%] flex justify-center'>
                    <h1 className='text-[35px] md:text-[35px] lg:text-[50px] xl:text-[60px] cursor-pointer hover:text-[#2596be] transition duration-300 font-[briem-bold]'>
                        Projects
                    </h1>
                </span>

                <div className="p-4 md:p-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-7xl mx-auto">
                        {projects.map((project) => (
                            <div
                                key={project.id}
                                className={`project-card group relative overflow-hidden rounded-xl`}
                            >
                                {/* Card Background with Glassmorphism Effect */}
                                <div className="card-inner h-full p-6">

                                    {/* Project Image */}
                                    <div className="relative overflow-hidden rounded-lg mb-4">
                                        <img
                                            src={project.image}
                                            alt={project.title}
                                            className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    </div>

                                    {/* Project Content */}
                                    <div className="space-y-4">
                                        <h3 className={`text-xl md:text-2xl font-[briem-bold] ${dark ? 'text-black' : 'text-white'} group-hover:text-[#2596be] transition-colors duration-300`}>
                                            {project.title}
                                        </h3>

                                        <p className={`text-sm md:text-base font-[briem-medium] ${dark ? 'text-gray-700' : 'text-gray-300'} line-clamp-3`}>
                                            {project.description}
                                        </p>

                                        {/* Technologies */}
                                        <div className="flex flex-wrap gap-2">
                                            {project.technologies.map((tech, index) => (
                                                <span
                                                    key={index}
                                                    className="tech-badge inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-[briem-medium] bg-white/10 backdrop-blur-sm border border-white/20"
                                                    style={{ color: tech.color }}
                                                >
                                                    {tech.icon}
                                                    {tech.name}
                                                </span>
                                            ))}
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex gap-3 pt-2">
                                            <p
                                                onClick={() => navigate(`/project/${project.id}`)}
                                                rel="noopener noreferrer"
                                                className={`project-button flex items-center gap-2 px-4 py-2 border-2 border-[#2596be] hover:bg-[#2596be] ${dark ? 'text-black hover:text-white' : 'text-white'} rounded-lg font-[briem-medium] text-sm flex-1 justify-center`}
                                            >
                                                See more
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Animated Background Elements */}
                                {/* <div className="absolute -top-4 -left-4 w-8 h-8  bg-[#2516be] rounded-full opacity-20 animate-pulse"></div>
                                <div className="absolute -bottom-4 -right-4 w-6 h-6 bg-[#2596be] rounded-full opacity-30 animate-pulse delay-1000"></div> */}
                            </div>
                        ))}
                    </div>
                </div>
            </Element>
        </>
    );
};


{"name": "yahya_codes", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.4.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tsparticles/all": "^3.3.0", "@tsparticles/basic": "^3.3.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.3.0", "aos": "^2.3.4", "flowbite-react": "^0.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "react-type-animation": "^3.2.0", "sass": "^1.74.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.3"}}